server:
  port: 9091

spring:
  application:
    name: prometheus-receiver

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

logging:
  file:
    name: logs/prometheus-receiver.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      total-size-cap: 100MB
      max-history: 30
      clean-history-on-start: true