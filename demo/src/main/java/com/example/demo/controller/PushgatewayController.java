package com.example.demo.controller;

import com.example.demo.model.MetricsRequest;
import com.example.demo.service.MetricsLoggingService;
import com.example.demo.service.MetricsProcessingService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/metrics")
public class PushgatewayController {

    @Autowired
    private MetricsLoggingService loggingService;
    @Autowired
    private MetricsProcessingService metricsProcessingService;

    @PostMapping(value = "/job**", consumes = {"text/plain", "application/x-protobuf", "*/*"})
    public ResponseEntity<String> receiveAllMetricsPost(
            @RequestBody(required = false) String metricsData,
            @RequestParam(required = false) Map<String, String> queryParams,
            HttpServletRequest request) {
        return getResponseEntity(metricsData, queryParams, request, "POST");
    }

    @DeleteMapping(value = "/job**", consumes = {"text/plain", "application/x-protobuf", "*/*"})
    public ResponseEntity<String> receiveAllMetricsPutDelete(
            @RequestBody(required = false) String metricsData,
            @RequestParam(required = false) Map<String, String> queryParams,
            HttpServletRequest request) {
        log.info("Received DELETE request to /metrics metricsData : {} queryParams : {} url : {}", metricsData, queryParams, request.getRequestURI());
        return ResponseEntity.ok("Metrics received successfully");
    }

    private ResponseEntity<String> getResponseEntity(String metricsData, Map<String, String> queryParams, HttpServletRequest request, String method) {
        try {
            String fullPath = request.getRequestURI();
            log.info("Received request at path: {}", fullPath);

            // 解析job和instance
            String job = extractJobFromPath(fullPath);
            String instance = extractInstanceFromPath(fullPath);
            Map<String, String> pathLabels = extractAllLabelsFromPath(fullPath);

            // 解析job结构
            JobStructure jobStructure = parseJobStructure(job);
            log.info("Parsed job structure - Original: {}, Type: {}, AppId: {}, Name: {}",
                    job, jobStructure.getJobType(), jobStructure.getAppId(), jobStructure.getName());

            if (queryParams == null) {
                queryParams = new HashMap<>();
            }

            MetricsRequest metricsRequest = MetricsRequest.builder()
                    .job(job)
                    .jobType(jobStructure.getJobType())
                    .appId(jobStructure.getAppId())
                    .name(jobStructure.getName())
                    .instance(instance)
                    .metricsData(metricsData)
                    .queryParams(queryParams)
                    .headers(extractHeaders(request))
                    .requestPath(fullPath)
                    .requestMethod(request.getMethod())
                    .pathLabels(pathLabels)
                    .build();
            loggingService.logMetricsRequest(metricsRequest);

            // 处理和解析指标数据
            metricsProcessingService.processMetrics(metricsRequest);

            return ResponseEntity.ok("Metrics received successfully");
        } catch (Exception e) {
            log.error("Error processing metrics request", e);
            return ResponseEntity.badRequest().body("Error processing metrics: " + e.getMessage());
        }
    }

    private String extractJobFromPath(String fullPath) {
        String pattern = "/metrics/job";
        if (fullPath.startsWith(pattern)) {
            String remaining = fullPath.substring(pattern.length());

            // 处理 job@base64/xxx 格式
            if (remaining.startsWith("@base64/")) {
                String base64Part = remaining.substring("@base64/".length());
                int nextSlash = base64Part.indexOf('/');
                if (nextSlash > 0) {
                    String encodedJob = base64Part.substring(0, nextSlash);
                    return decodeBase64Job(encodedJob);
                } else {
                    return decodeBase64Job(base64Part);
                }
            }
            // 处理普通 job/xxx 格式
            else if (remaining.startsWith("/")) {
                remaining = remaining.substring(1); // 去掉开头的 /
                int nextSlash = remaining.indexOf('/');
                if (nextSlash > 0) {
                    return remaining.substring(0, nextSlash);
                } else {
                    return remaining;
                }
            }
        }
        return "unknown";
    }

    private String extractInstanceFromPath(String fullPath) {
        // 查找 /instance/ 模式
        String instancePattern = "/instance/";
        int instanceIndex = fullPath.indexOf(instancePattern);
        if (instanceIndex > 0) {
            String afterInstance = fullPath.substring(instanceIndex + instancePattern.length());
            int nextSlash = afterInstance.indexOf('/');
            if (nextSlash > 0) {
                return afterInstance.substring(0, nextSlash);
            } else {
                return afterInstance;
            }
        }
        return null;
    }

    private boolean isPathKeyword(String part) {
        return "indicator".equals(part) || "instance".equals(part) ||
                "port".equals(part) || "ip".equals(part);
    }

    private String decodeBase64Job(String encodedJob) {
        try {
            byte[] decodedBytes = java.util.Base64.getDecoder().decode(encodedJob);
            return new String(decodedBytes, java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.warn("Failed to decode base64 job: {}", encodedJob, e);
            return encodedJob; // 返回原始值
        }
    }

    private Map<String, String> extractHeaders(HttpServletRequest request) {
        return java.util.Collections.list(request.getHeaderNames())
                .stream()
                .collect(Collectors.toMap(
                        name -> name,
                        request::getHeader
                ));
    }

    private JobStructure parseJobStructure(String job) {
        if (job == null || job.trim().isEmpty() || "unknown".equals(job)) {
            return JobStructure.builder()
                    .jobType("unknown")
                    .appId(null)
                    .name(null)
                    .build();
        }

        String[] parts = job.split("/");

        if (parts.length == 1) {
            // 只有一个部分，作为jobType
            return JobStructure.builder()
                    .jobType(parts[0])
                    .appId(null)
                    .name(null)
                    .build();
        } else if (parts.length == 2) {
            // 有两个部分：jobType和appId
            return JobStructure.builder()
                    .jobType(parts[0])
                    .appId(parts[1])
                    .name(null)
                    .build();
        } else if (parts.length >= 3) {
            // 有三个或更多部分，取前三个：jobType、appId、name
            return JobStructure.builder()
                    .jobType(parts[0])
                    .appId(parts[1])
                    .name(parts[2])
                    .build();
        } else {
            // 空字符串情况
            return JobStructure.builder()
                    .jobType("unknown")
                    .appId(null)
                    .name(null)
                    .build();
        }
    }

    private Map<String, String> extractAllLabelsFromPath(String fullPath) {
        Map<String, String> labels = new HashMap<>();
        // 解析路径中的所有标签对
        String[] parts = fullPath.split("/");
        for (int i = 0; i < parts.length - 1; i++) {
            String current = parts[i];
            String next = parts[i + 1];

            // 跳过固定的路径部分
            if ("metrics".equals(current) || current.startsWith("job") || current.isEmpty()) {
                continue;
            }
            // 添加标签对
            if (!next.isEmpty() && !isPathKeyword(next)) {
                labels.put(current, next);
                i++; // 跳过下一个元素，因为它已经被处理了
            }
        }
        return labels;
    }

    // 更新内部类，添加name字段
    @Data
    @Builder
    private static class JobStructure {
        private String jobType;
        private String appId;
        private String name;
    }
}
