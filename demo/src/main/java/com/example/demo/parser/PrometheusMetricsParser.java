package com.example.demo.parser;

import com.example.demo.model.PrometheusMetric;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class PrometheusMetricsParser {
    
    private static final Pattern HELP_PATTERN = Pattern.compile("^# HELP\\s+(\\S+)\\s+(.*)$");
    private static final Pattern TYPE_PATTERN = Pattern.compile("^# TYPE\\s+(\\S+)\\s+(\\S+)$");
    private static final Pattern METRIC_PATTERN = Pattern.compile("^([a-zA-Z_:][a-zA-Z0-9_:]*)(\\{[^}]*\\})?\\s+([+-]?[0-9]*\\.?[0-9]+(?:[eE][+-]?[0-9]+)?)(?:\\s+(\\d+))?$");
    private static final Pattern LABEL_PATTERN = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)=\"([^\"]*)\",?");
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    public List<PrometheusMetric> parseMetrics(String metricsData, String job, String jobType, String appId, String name, String instance) {
        List<PrometheusMetric> metrics = new ArrayList<>();

        if (metricsData == null || metricsData.trim().isEmpty()) {
            return metrics;
        }

        String[] lines = metricsData.split("\n");
        Map<String, String> helpMap = new HashMap<>();
        Map<String, String> typeMap = new HashMap<>();

        // 第一遍：收集HELP和TYPE信息
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            Matcher helpMatcher = HELP_PATTERN.matcher(line);
            if (helpMatcher.matches()) {
                helpMap.put(helpMatcher.group(1), helpMatcher.group(2));
                continue;
            }

            Matcher typeMatcher = TYPE_PATTERN.matcher(line);
            if (typeMatcher.matches()) {
                typeMap.put(typeMatcher.group(1), typeMatcher.group(2));
                continue;
            }
        }

        // 第二遍：解析指标数据
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty() || line.startsWith("#")) continue;

            try {
                PrometheusMetric metric = parseMetricLine(line, helpMap, typeMap, job, jobType, appId, name, instance);
                if (metric != null) {
                    metrics.add(metric);
                }
            } catch (Exception e) {
                log.warn("Failed to parse metric line: {}", line, e);
            }
        }

        return metrics;
    }

    private PrometheusMetric parseMetricLine(String line, Map<String, String> helpMap,
                                             Map<String, String> typeMap, String job, String jobType, String appId, String name, String instance) {
        Matcher matcher = METRIC_PATTERN.matcher(line);
        if (!matcher.matches()) {
            log.warn("Invalid metric line format: {}", line);
            return null;
        }

        String metricName = matcher.group(1);
        String labelsStr = matcher.group(2);
        String valueStr = matcher.group(3);
        String timestampStr = matcher.group(4);

        // 解析标签
        Map<String, String> labels = parseLabels(labelsStr);

        // 解析数值
        BigDecimal value;
        try {
            value = new BigDecimal(valueStr);
        } catch (NumberFormatException e) {
            log.warn("Invalid metric value: {}", valueStr);
            return null;
        }

        // 解析时间戳
        Instant timestamp = timestampStr != null ?
                Instant.ofEpochMilli(Long.parseLong(timestampStr)) :
                Instant.now();

        return PrometheusMetric.builder()
                .metricName(metricName)
                .metricType(typeMap.getOrDefault(metricName, "untyped"))
                .description(helpMap.getOrDefault(metricName, ""))
                .labels(labels)
                .value(value)
                .timestamp(timestamp)
                .job(job)
                .jobType(jobType)
                .appId(appId)
                .name(name)
                .instance(instance)
                .build();
    }
    
    private Map<String, String> parseLabels(String labelsStr) {
        Map<String, String> labels = new HashMap<>();
        
        if (labelsStr == null || labelsStr.trim().isEmpty()) {
            return labels;
        }
        
        // 移除大括号
        String cleanLabels = labelsStr.trim();
        if (cleanLabels.startsWith("{") && cleanLabels.endsWith("}")) {
            cleanLabels = cleanLabels.substring(1, cleanLabels.length() - 1);
        }
        
        if (cleanLabels.trim().isEmpty()) {
            return labels;
        }
        
        Matcher labelMatcher = LABEL_PATTERN.matcher(cleanLabels);
        while (labelMatcher.find()) {
            String key = labelMatcher.group(1);
            String value = labelMatcher.group(2);
            labels.put(key, value);
        }
        
        return labels;
    }
    
    public String labelsToJson(Map<String, String> labels) {
        try {
            return objectMapper.writeValueAsString(labels);
        } catch (Exception e) {
            log.warn("Failed to convert labels to JSON: {}", labels, e);
            return "{}";
        }
    }
}