package com.example.demo.service;

import cn.hutool.json.JSONUtil;
import com.example.demo.model.MetricsRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MetricsLoggingService {

    public void logMetricsRequest(MetricsRequest request) {
        log.info("=== Prometheus Pushgateway Request Received ===");
        log.info("{}", JSONUtil.toJsonStr(request));
    }
}
