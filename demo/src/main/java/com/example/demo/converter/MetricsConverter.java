package com.example.demo.converter;

import com.example.demo.model.ClickHouseMetric;
import com.example.demo.model.PrometheusMetric;
import com.example.demo.parser.PrometheusMetricsParser;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class MetricsConverter {
    
    private final PrometheusMetricsParser parser;
    
    public List<ClickHouseMetric> convertToClickHouseFormat(List<PrometheusMetric> prometheusMetrics) {
        return prometheusMetrics.stream()
                .map(this::convertSingleMetric)
                .collect(Collectors.toList());
    }

    private ClickHouseMetric convertSingleMetric(PrometheusMetric metric) {
        Map<String, String> labels = metric.getLabels();

        return ClickHouseMetric.builder()
                .timestamp(metric.getTimestamp())
                .job(metric.getJob())
                .jobType(metric.getJobType())
                .appId(metric.getAppId())
                .name(metric.getName())
                .instance(metric.getInstance())
                .metricName(metric.getMetricName())
                .metricType(metric.getMetricType())
                .description(metric.getDescription())
                .labelsJson(parser.labelsToJson(labels))
                .value(metric.getValue())
                .clusterId(labels.get("clusterId"))
                .deviceType(labels.get("deviceType"))
                .deviceId(labels.get("deviceId"))
                .ip(labels.get("ip"))
                .port(labels.get("port"))
                .connectPort(labels.get("connectPort"))
                .build();
    }
}