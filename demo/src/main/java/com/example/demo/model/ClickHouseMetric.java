package com.example.demo.model;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
public class ClickHouseMetric {
    private Instant timestamp;
    private String job;
    private String jobType;
    private String appId;
    private String name;
    private String instance;
    private String metricName;
    private String metricType;
    private String description;
    private String labelsJson; // JSON格式存储标签
    private BigDecimal value;
    private String clusterId;
    private String deviceType;
    private String deviceId;
    private String ip;
    private String port;
    private String connectPort;
}
