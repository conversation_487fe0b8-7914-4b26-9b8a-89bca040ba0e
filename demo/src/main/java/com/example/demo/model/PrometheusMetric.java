package com.example.demo.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;

@Data
@Builder
public class PrometheusMetric {
    private String metricName;
    private String metricType;
    private String description;
    private Map<String, String> labels;
    private BigDecimal value;
    private Instant timestamp;
    private String job;
    private String jobType;
    private String appId;
    private String name;
    private String instance;
}