package com.example.demo.model;


import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@Builder
public class MetricsRequest {
    private String job;
    private String jobType;
    private String appId;
    private String name;
    private String instance;
    private String metricsData;
    private Map<String, String> queryParams;
    private Map<String, String> headers;
    private String requestPath;
    private String requestMethod;
    private Map<String, String> pathLabels;
}
